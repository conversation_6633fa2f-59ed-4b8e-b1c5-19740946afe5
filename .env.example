# Flask LLM Knowledge Assistant - Environment Configuration
# Copy this file to .env and fill in your actual values

# Required: OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Security (IMPORTANT: Change in production!)
SECRET_KEY=your-secret-key-change-in-production-use-long-random-string

# Application Configuration
FLASK_ENV=production
FLASK_DEBUG=false

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Vector Database Configuration
VECTOR_DB_PATH=vector_db

# LLM Configuration (Optional - defaults will be used if not set)
LLM_MODEL=gpt-3.5-turbo
EMBEDDING_MODEL=text-embedding-ada-002
MAX_TOKENS=1000
TEMPERATURE=0.1

# RAG Configuration (Optional - defaults will be used if not set)
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
TOP_K_RESULTS=5

# Logging (Optional)
LOG_LEVEL=INFO

# For Portainer deployment, you can also set these in the container environment
# instead of using a .env file
