# Deployment Guide for Flask LLM Knowledge Assistant

## Overview
This guide covers deploying the Flask LLM Knowledge Assistant to your home server using Portainer.

## Prerequisites
- Docker installed on your home server
- <PERSON><PERSON><PERSON> running on your home server
- OpenAI API key

## Quick Start

### 1. Build the Docker Image

```bash
# Clone/copy your code to the server or build locally and push to registry
docker build -t llm-knowledge-assistant:latest .
```

### 2. Environment Variables for Portainer

When creating the container in Portainer, set these environment variables:

**Required:**
- `OPENAI_API_KEY`: Your OpenAI API key
- `SECRET_KEY`: A long, random secret key for Flask sessions

**Optional (with defaults):**
- `FLASK_ENV`: `production` (default)
- `UPLOAD_FOLDER`: `/app/uploads` (default)
- `VECTOR_DB_PATH`: `/app/vector_db` (default)
- `MAX_CONTENT_LENGTH`: `16777216` (16MB, default)
- `LLM_MODEL`: `gpt-3.5-turbo` (default)
- `EMBEDDING_MODEL`: `text-embedding-ada-002` (default)
- `MAX_TOKENS`: `1000` (default)
- `TEMPERATURE`: `0.1` (default)

### 3. Volume Mounts for Portainer

Create these volume mounts to persist data:

- **uploads**: `/app/uploads` - Stores uploaded documents
- **vector_db**: `/app/vector_db` - Stores the vector database
- **logs**: `/app/logs` - Application logs

### 4. Port Mapping

- Container port: `5000`
- Host port: Choose available port (e.g., `8080:5000`)

### 5. Network Configuration

- Use bridge network or create custom network
- Ensure the port is accessible from your network

## Portainer Container Configuration

### Container Settings
```
Name: llm-knowledge-assistant
Image: llm-knowledge-assistant:latest
```

### Network & Ports
```
Network: bridge (or custom network)
Port mapping: 8080:5000 (or your preferred host port)
```

### Environment Variables
```
OPENAI_API_KEY=sk-your-actual-api-key-here
SECRET_KEY=your-very-long-random-secret-key-here
FLASK_ENV=production
```

### Volumes
```
/path/on/host/uploads:/app/uploads
/path/on/host/vector_db:/app/vector_db
/path/on/host/logs:/app/logs
```

### Restart Policy
```
Restart policy: Unless stopped
```

## Health Check

The container includes a health check that monitors the `/health` endpoint. Portainer will show the container as healthy when the API is responding correctly.

## Accessing the API

Once deployed, access your API at:
- `http://your-server-ip:8080` (replace with your actual IP and port)

### API Endpoints
- `GET /health` - Health check
- `POST /upload-document` - Upload documents
- `POST /ask-question` - Ask questions
- `GET /vector-store/stats` - Vector store statistics
- `DELETE /clear-knowledge-base` - Clear knowledge base

## Security Considerations

1. **Change the SECRET_KEY**: Use a long, random string
2. **Firewall**: Only expose necessary ports
3. **HTTPS**: Consider using a reverse proxy (Traefik, Nginx) for HTTPS
4. **API Key**: Keep your OpenAI API key secure
5. **Network**: Use custom Docker networks for better isolation

## Monitoring

- Check container logs in Portainer for any issues
- Monitor the health check status
- Watch resource usage (CPU, memory)

## Backup

Regularly backup these volumes:
- `/app/uploads` - Your uploaded documents
- `/app/vector_db` - The processed vector database

## Troubleshooting

### Container won't start
- Check environment variables are set correctly
- Verify OpenAI API key is valid
- Check container logs in Portainer

### API not responding
- Verify port mapping is correct
- Check if container is healthy
- Review application logs

### Out of memory
- Increase container memory limits
- Consider using smaller embedding models
- Monitor vector database size

## Updating

To update the application:
1. Build new image with updated code
2. Stop the old container in Portainer
3. Create new container with same configuration
4. Start the new container

## Development Testing

Before deploying to production, test locally:

```bash
# Test with docker-compose
docker-compose up

# Or test development version
docker-compose -f docker-compose.dev.yml up
```
