#!/bin/bash

# Flask LLM Knowledge Assistant - Deployment Script
# This script helps build and prepare the application for Portainer deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="llm-knowledge-assistant"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo -e "${BLUE}🚀 Flask LLM Knowledge Assistant Deployment Script${NC}"
echo "=================================================="

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running"

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Please create one based on .env.example"
    echo "Would you like to copy .env.example to .env? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        cp .env.example .env
        print_status "Created .env file from .env.example"
        print_warning "Please edit .env file with your actual values before deploying!"
    else
        print_error "Cannot proceed without .env file"
        exit 1
    fi
fi

# Build the Docker image
echo -e "\n${BLUE}🔨 Building Docker image...${NC}"
if docker build -t "$FULL_IMAGE_NAME" .; then
    print_status "Docker image built successfully: $FULL_IMAGE_NAME"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Test the image
echo -e "\n${BLUE}🧪 Testing the Docker image...${NC}"
if docker run --rm --env-file .env -p 5001:5000 -d --name llm-test "$FULL_IMAGE_NAME"; then
    sleep 10
    if curl -f http://localhost:5001/health > /dev/null 2>&1; then
        print_status "Health check passed"
        docker stop llm-test > /dev/null 2>&1
    else
        print_error "Health check failed"
        docker stop llm-test > /dev/null 2>&1
        exit 1
    fi
else
    print_error "Failed to start test container"
    exit 1
fi

# Show image info
echo -e "\n${BLUE}📊 Image Information:${NC}"
docker images "$FULL_IMAGE_NAME"

# Deployment instructions
echo -e "\n${BLUE}📋 Deployment Instructions for Portainer:${NC}"
echo "=============================================="
echo "1. Image name: $FULL_IMAGE_NAME"
echo "2. Port mapping: 8080:5000 (or your preferred port)"
echo "3. Environment variables (from your .env file):"
echo "   - OPENAI_API_KEY=<your-api-key>"
echo "   - SECRET_KEY=<your-secret-key>"
echo "   - FLASK_ENV=production"
echo "4. Volume mounts:"
echo "   - /path/on/host/uploads:/app/uploads"
echo "   - /path/on/host/vector_db:/app/vector_db"
echo "   - /path/on/host/logs:/app/logs"
echo ""
echo "5. Restart policy: Unless stopped"
echo ""
print_status "Image is ready for deployment!"
print_warning "Don't forget to set your actual environment variables in Portainer!"

# Optional: Save image to tar file for transfer
echo -e "\n${YELLOW}💾 Would you like to save the image to a tar file for transfer? (y/n)${NC}"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    TAR_FILE="${IMAGE_NAME}-${IMAGE_TAG}.tar"
    echo "Saving image to $TAR_FILE..."
    if docker save -o "$TAR_FILE" "$FULL_IMAGE_NAME"; then
        print_status "Image saved to $TAR_FILE"
        echo "You can transfer this file to your server and load it with:"
        echo "docker load -i $TAR_FILE"
    else
        print_error "Failed to save image"
    fi
fi

echo -e "\n${GREEN}🎉 Deployment preparation complete!${NC}"
echo "See DEPLOYMENT.md for detailed Portainer setup instructions."
