version: '3.8'

services:
  llm-knowledge-assistant-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: llm-knowledge-assistant-dev
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=true
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key}
      - UPLOAD_FOLDER=/app/uploads
      - VECTOR_DB_PATH=/app/vector_db
      - MAX_CONTENT_LENGTH=16777216
    volumes:
      # Mount source code for development
      - .:/app
      - /app/__pycache__
      - uploads_data_dev:/app/uploads
      - vector_db_data_dev:/app/vector_db
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - llm_network_dev

volumes:
  uploads_data_dev:
    driver: local
  vector_db_data_dev:
    driver: local

networks:
  llm_network_dev:
    driver: bridge
