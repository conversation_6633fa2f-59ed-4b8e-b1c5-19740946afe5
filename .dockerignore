# Git
.git
.gitignore
.gitattributes

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Test files
tests/
test_*
*_test.py
pytest.ini

# Build artifacts
build/
dist/
*.egg-info/

# Docker development files
docker-compose.yml
docker-compose.*.yml
Dockerfile.dev

# Logs
logs/
*.log

# Runtime data directories
uploads/*
vector_db/*
!uploads/.gitkeep
!vector_db/.gitkeep

# Documentation (keep README.md)
*.md
!README.md

# Example files
example_usage.py

# Temporary files
tmp/
temp/
.tmp
