# Flask LLM Knowledge Assistant - Makefile

.PHONY: help build run dev test clean deploy health logs

# Variables
IMAGE_NAME = llm-knowledge-assistant
IMAGE_TAG = latest
CONTAINER_NAME = llm-knowledge-assistant
DEV_CONTAINER_NAME = llm-knowledge-assistant-dev

help: ## Show this help message
	@echo "Flask LLM Knowledge Assistant - Available Commands:"
	@echo "=================================================="
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

build: ## Build the production Docker image
	@echo "🔨 Building production Docker image..."
	docker build -t $(IMAGE_NAME):$(IMAGE_TAG) .

build-dev: ## Build the development Docker image
	@echo "🔨 Building development Docker image..."
	docker build -f Dockerfile.dev -t $(IMAGE_NAME):dev .

run: ## Run the production container
	@echo "🚀 Starting production container..."
	docker-compose up -d
	@echo "✅ Container started. Access at http://localhost:5000"

dev: ## Run the development container with hot reload
	@echo "🚀 Starting development container..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "✅ Development container started. Access at http://localhost:5000"

test: ## Run tests
	@echo "🧪 Running tests..."
	python -m pytest tests/ -v

test-docker: ## Run tests in Docker container
	@echo "🧪 Running tests in Docker..."
	docker run --rm -v $(PWD):/app -w /app $(IMAGE_NAME):$(IMAGE_TAG) python -m pytest tests/ -v

health: ## Check container health
	@echo "🏥 Checking container health..."
	@curl -f http://localhost:5000/health && echo "✅ Health check passed" || echo "❌ Health check failed"

logs: ## Show container logs
	@echo "📋 Showing container logs..."
	docker-compose logs -f

logs-dev: ## Show development container logs
	@echo "📋 Showing development container logs..."
	docker-compose -f docker-compose.dev.yml logs -f

stop: ## Stop the production container
	@echo "🛑 Stopping production container..."
	docker-compose down

stop-dev: ## Stop the development container
	@echo "🛑 Stopping development container..."
	docker-compose -f docker-compose.dev.yml down

clean: ## Clean up Docker images and containers
	@echo "🧹 Cleaning up..."
	docker-compose down -v
	docker-compose -f docker-compose.dev.yml down -v
	docker rmi $(IMAGE_NAME):$(IMAGE_TAG) $(IMAGE_NAME):dev 2>/dev/null || true
	docker system prune -f

deploy: ## Prepare for deployment (build and test)
	@echo "🚀 Preparing for deployment..."
	./deploy.sh

save-image: ## Save Docker image to tar file
	@echo "💾 Saving Docker image..."
	docker save -o $(IMAGE_NAME)-$(IMAGE_TAG).tar $(IMAGE_NAME):$(IMAGE_TAG)
	@echo "✅ Image saved to $(IMAGE_NAME)-$(IMAGE_TAG).tar"

load-image: ## Load Docker image from tar file
	@echo "📥 Loading Docker image..."
	docker load -i $(IMAGE_NAME)-$(IMAGE_TAG).tar
	@echo "✅ Image loaded"

env: ## Create .env file from example
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "✅ Created .env file from .env.example"; \
		echo "⚠️  Please edit .env with your actual values"; \
	else \
		echo "⚠️  .env file already exists"; \
	fi

install: ## Install Python dependencies locally
	@echo "📦 Installing Python dependencies..."
	pip install -r requirements.txt

format: ## Format code with black (if available)
	@echo "🎨 Formatting code..."
	@black . 2>/dev/null || echo "⚠️  black not installed, skipping formatting"

lint: ## Lint code with flake8 (if available)
	@echo "🔍 Linting code..."
	@flake8 . 2>/dev/null || echo "⚠️  flake8 not installed, skipping linting"

backup: ## Backup volumes
	@echo "💾 Creating backup of volumes..."
	@mkdir -p backups
	docker run --rm -v llm_uploads_data:/data -v $(PWD)/backups:/backup alpine tar czf /backup/uploads-$(shell date +%Y%m%d-%H%M%S).tar.gz -C /data .
	docker run --rm -v llm_vector_db_data:/data -v $(PWD)/backups:/backup alpine tar czf /backup/vector_db-$(shell date +%Y%m%d-%H%M%S).tar.gz -C /data .
	@echo "✅ Backup completed in backups/ directory"

status: ## Show container status
	@echo "📊 Container Status:"
	@docker-compose ps 2>/dev/null || echo "No containers running"
