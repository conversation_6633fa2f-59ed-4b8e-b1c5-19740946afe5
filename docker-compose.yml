version: '3.8'

services:
  llm-knowledge-assistant:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: llm-knowledge-assistant
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - UPLOAD_FOLDER=/app/uploads
      - VECTOR_DB_PATH=/app/vector_db
      - MAX_CONTENT_LENGTH=16777216
    volumes:
      # Persistent storage for uploads and vector database
      - uploads_data:/app/uploads
      - vector_db_data:/app/vector_db
      - logs_data:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - llm_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.llm-assistant.rule=Host(`llm-assistant.local`)"
      - "traefik.http.services.llm-assistant.loadbalancer.server.port=5000"

volumes:
  uploads_data:
    driver: local
  vector_db_data:
    driver: local
  logs_data:
    driver: local

networks:
  llm_network:
    driver: bridge
